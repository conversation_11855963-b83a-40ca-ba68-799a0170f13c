import { Injectable } from '@nestjs/common';
import {
  OrderStatus as MobileOrderStatus,
  mapMobileToBackendStatus,
  mapBackendToMobileStatus,
  OrderStatus,
  getDetailedInProgressStatus,
} from '../domain/order';
import { UpdateOrderStatusDto } from '../dto/update-order-status.dto';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import { HttpStatus } from '@nestjs/common';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { FilterOperator } from '@core/infrastructure/filtering/types/filter.types';
import { OrdersService } from '@app/business/order/orders/orders.service';
import { OrderAssignmentService } from '@app/business/order/orders/services/order-assignment.service';
import { OrderStatusService } from '@app/business/order/orders/services/order-status.service';
import { UsersService } from '@app/business/user/users/users.service';
import { VehiclesService } from '@app/business/vehicle/vehicles/vehicles.service';
import { FileStorageService } from '@app/core/file-storage/file-storage.service';
import { PriceSetsService } from '@app/business/pricing/price-sets/price-sets.service';
import { OrderStatus as BackendOrderStatus } from '@app/business/order/orders/domain/order.types';
import { DataSource } from 'typeorm';
import { OrderEntity } from '../../../order/orders/infrastructure/entities/order.entity';
import { OrderType } from '../../../../utils/types/order.types';
import { OrderItemRepository } from '../../../order/orders/infrastructure/repositories/order-item.repository';
import { TimeClockSessionService } from '../../../vehicle/time-clock-session/time-clock-session.service';
import { DEFAULT_DATETIME_FORMAT_12H } from '../../../pricing/constants/timezone.constants';
import { formatDateTime } from '../../../pricing/utils/date.utils';
import { OrderDetailsResponseDto } from '../dto/order-details-response.dto';
import { MobileOrderListItemDto } from '../dto/order-list-response.dto';
import { AddressDto } from '../../../order/orders/dto/address.dto';
import { FileUploadService } from '../../../../core/file-upload/services/file-upload.service';
import { FILE_UPLOAD_CONSTANTS } from '../../../../core/file-upload/constants/file-uploads.constant';
@Injectable()
export class MobileOrdersService {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly orderAssignmentService: OrderAssignmentService,
    private readonly orderStatusService: OrderStatusService,
    private readonly usersService: UsersService,
    private readonly vehiclesService: VehiclesService,
    private readonly fileStorageService: FileStorageService,
    private readonly priceSetsService: PriceSetsService,
    private readonly timeClockSessionService: TimeClockSessionService,
    private readonly dataSource: DataSource,
    private readonly orderItemRepository: OrderItemRepository,
    private readonly fileUploadService: FileUploadService,
  ) {}

  /**
   * Enhance order with service level information
   * This adds the service level to the metadata if it's not already present
   */
  private enhanceOrderWithServiceLevel(order: any): void {
    try {
      // If metadata doesn't exist, create it
      if (!order.metadata) {
        order.metadata = {};
      }

      // If service level is not in metadata, try to determine it
      if (!order.metadata.serviceLevel) {
        // First try to use the basePriceType as the service level if available
        if (order.basePriceType) {
          order.metadata.serviceLevel = order.basePriceType;
        }
        // Otherwise determine based on scheduled times
        else if (order.basePrice) {
          // Store the service level in metadata for future use
          order.metadata.serviceLevel = this.determineServiceLevel(order);
        }
      }
    } catch (error) {
      console.error('Error enhancing order with service level:', error);
    }
  }

  /**
   * Format an address entity into a readable summary string
   */
  private formatAddressSummary(address: any): string {
    if (!address) return '';

    const parts: string[] = [];

    if (address.addressLine1) {
      parts.push(address.addressLine1);
    }

    if (address.city || address.province || address.postalCode) {
      const cityParts: string[] = [];
      if (address.city) cityParts.push(address.city);
      if (address.province) cityParts.push(address.province);
      if (address.postalCode) cityParts.push(address.postalCode);
      parts.push(cityParts.join(', '));
    }

    return parts.join(', ');
  }

  /**
   * Determine the service level based on order information
   */
  private determineServiceLevel(order: any): string {
    // Default service levels based on delivery time
    if (order.scheduledCollectionTime && order.scheduledDeliveryTime) {
      const pickupDate = new Date(order.scheduledCollectionTime);
      const deliveryDate = new Date(order.scheduledDeliveryTime);

      const isSameDay =
        pickupDate.getDate() === deliveryDate.getDate() &&
        pickupDate.getMonth() === deliveryDate.getMonth() &&
        pickupDate.getFullYear() === deliveryDate.getFullYear();

      // Calculate hours difference
      const hoursDiff =
        (deliveryDate.getTime() - pickupDate.getTime()) / (1000 * 60 * 60);

      if (isSameDay) {
        if (hoursDiff <= 2) return 'Express';
        if (hoursDiff <= 4) return 'Same Day';
        return 'Standard';
      } else {
        return 'Next Day';
      }
    }

    return 'Standard';
  }

  async getDriverOrders(
    driverId: string,
    status?: MobileOrderStatus | MobileOrderStatus[],
  ) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    console.log(
      `Getting orders for driver ${driverId} with tenant ${driver.tenantId}`,
    );

    // Use a query builder to get all orders assigned to this driver
    // Get all statuses that we want to exclude
    const excludedStatuses = [
      BackendOrderStatus.Draft,
      BackendOrderStatus.Completed,
    ];

    // Create a query builder that joins with address tables to get address details
    const orders = (await this.dataSource
      .createQueryBuilder()
      .select('orders')
      .from('orders', 'orders')
      .leftJoinAndSelect('orders.collectionAddress', 'collectionAddress')
      .leftJoinAndSelect('orders.deliveryAddress', 'deliveryAddress')
      .leftJoinAndSelect('orders.requestedBy', 'requestedBy')
      .leftJoinAndSelect('orders.priceSet', 'priceSet')
      .leftJoinAndSelect('orders.customer', 'customer')
      .where('orders.tenant_id = :tenantId', { tenantId: driver.tenantId })
      .andWhere('orders.assigned_driver_id = :driverId', { driverId })
      .andWhere('orders.is_deleted = :isDeleted', { isDeleted: false })
      .andWhere('orders.status NOT IN (:...excludedStatuses)', {
        excludedStatuses,
      })
      .getMany()) as OrderEntity[] as any;

    console.log(`Found ${orders.length} orders assigned to driver ${driverId}`);

    let filteredOrders = orders.map((odr) => ({
      ...odr,
      collectionAddress: this.extractAddressFromOrder(odr.collectionAddress),
      deliveryAddress: this.extractAddressFromOrder(odr.deliveryAddress),
      requestedByName: odr.requestedBy?.name,
      serviceLevel: odr.priceSet?.name,
      customerName: odr.customer.contactName || odr.customer.email,
    }));

    if (status) {
      // Convert mobile status to backend status
      const backendStatuses = Array.isArray(status)
        ? status.map((s) => mapMobileToBackendStatus(s))
        : [mapMobileToBackendStatus(status)];

      // Filter by status
      filteredOrders = filteredOrders.filter((order) =>
        backendStatuses.includes(order.status),
      );

      console.log(
        `After status filtering, found ${filteredOrders.length} orders`,
      );
    }

    return filteredOrders;
  }

  async getUnassignedOrders(
    driverId: string,
    status?: MobileOrderStatus | MobileOrderStatus[],
  ) {
    // Get all statuses that we want to include
    const includedStatuses = [
      BackendOrderStatus.Pending,
      BackendOrderStatus.Submitted,
    ];

    const driver = await this.usersService.findById(driverId);
    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    // Create a query builder that joins with address tables to get address details
    const orders = (await this.dataSource
      .createQueryBuilder()
      .select('orders')
      .from('orders', 'orders')
      .leftJoinAndSelect('orders.collectionAddress', 'collectionAddress')
      .leftJoinAndSelect('orders.deliveryAddress', 'deliveryAddress')
      .leftJoinAndSelect('orders.requestedBy', 'requestedBy')
      .leftJoinAndSelect('orders.priceSet', 'priceSet')
      .leftJoinAndSelect('orders.customer', 'customer')
      .where('orders.tenant_id = :tenantId', { tenantId: driver.tenantId })
      .andWhere('orders.assigned_driver_id IS NULL')
      .andWhere('orders.is_deleted = :isDeleted', { isDeleted: false })
      .andWhere('orders.status IN (:...includedStatuses)', {
        includedStatuses,
      })
      .getMany()) as OrderEntity[] as any;

    console.log(
      `Found ${orders.length} unassigned orders for tenant ${driver.tenantId}`,
    );

    const orderIds = orders.map((order) => order.id);
    const allItems = await this.orderItemRepository.getBulk(orderIds);

    let filteredOrders = orders.map((odr) => {
      return {
        ...odr,
        collectionAddress: this.extractAddressFromOrder(odr.collectionAddress),
        deliveryAddress: this.extractAddressFromOrder(odr.deliveryAddress),
        requestedByName: odr.requestedBy?.name,
        serviceLevel: odr.priceSet?.name,
        customerName: odr.customer.contactName || odr.customer.email,
        companyName: odr.customer?.companyName,
        items: allItems[odr.id] || [],
      };
    });

    if (status) {
      // Convert mobile status to backend status
      const backendStatuses = Array.isArray(status)
        ? status.map((s) => mapMobileToBackendStatus(s))
        : [mapMobileToBackendStatus(status)];

      // Filter by status
      filteredOrders = filteredOrders.filter((order) =>
        backendStatuses.includes(order.status),
      );

      console.log(
        `After status filtering, found ${filteredOrders.length} orders`,
      );
    }

    return filteredOrders;
  }

  async getOrderById(
    orderId: string,
    driverId: string,
    getForUnassigned = false,
  ) {
    // Get the driver to determine their tenant ID

    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      const query = await this.dataSource
        .createQueryBuilder()
        .select('orders')
        .from('orders', 'orders')
        .leftJoinAndSelect('orders.collectionAddress', 'collectionAddress')
        .leftJoinAndSelect('orders.deliveryAddress', 'deliveryAddress')
        .leftJoinAndSelect('orders.customer', 'customer')
        .leftJoinAndSelect('orders.priceSet', 'priceSet')
        .leftJoinAndSelect('orders.requestedBy', 'requestedBy')
        .where('orders.id = :orderId', { orderId })
        .andWhere('orders.tenant_id = :tenantId', {
          tenantId: driver.tenantId,
        });

      if (getForUnassigned) {
        query.andWhere('orders.assigned_driver_id IS NULL');
        query.andWhere('orders.status IN (:...statuses)', {
          statuses: [BackendOrderStatus.Pending, BackendOrderStatus.Submitted],
        });
      }

      const order = (await query.getOne()) as OrderEntity as any;

      if (!order) {
        throw new AppException(
          `Order with ID ${orderId} not found`,
          ErrorCode.ORDER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      const orderItems = await this.orderItemRepository.findByOrderId(orderId);
      order.items = orderItems || [];

      order.deliveryAddress = this.extractAddressFromOrder(
        order.deliveryAddress,
      );
      if (order.requestedBy) {
        order.requestedByName = order.requestedBy?.name;
      }
      // Add customer name for easier access
      if (order.customer) {
        order.customerName =
          order.customer?.contactName || order.customer.email;
        order.companyName = order.customer?.companyName;
      }
      if (order.priceSet) {
        order.serviceLevel = order.priceSet?.name;
      }
      if (order.collectionAddress) {
        order.collectionAddress = this.extractAddressFromOrder(
          order.collectionAddress,
        );
      }
      if (order.deliveryAddress) {
        order.deliveryAddress = this.extractAddressFromOrder(
          order.deliveryAddress,
        );
      }

      return order;
    } catch {
      throw new AppException(
        `Order with ID ${orderId} not found`,
        ErrorCode.ORDER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async getDriverOrderById(driverId: string, orderId: string) {
    // Get the driver to determine their tenant ID

    try {
      // Get order with service level information and related entities
      const order = await this.getOrderById(orderId, driverId);

      const attachments = await this.ordersService.getOrderAttachments(orderId);
      order.attachments = attachments;

      if (order.assignedDriverId !== driverId) {
        throw new AppException(
          `Order with ID ${orderId} is not assigned to driver ${driverId}`,
          ErrorCode.ORDER_NOT_ASSIGNED_TO_DRIVER,
          HttpStatus.FORBIDDEN,
        );
      }

      return order;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        `Order with ID ${orderId} not found`,
        ErrorCode.ORDER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async updateOrderStatus(
    driverId: string,
    orderId: string,
    updateDto: UpdateOrderStatusDto,
  ) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    // Verify order exists and is assigned to driver
    const order = await this.getDriverOrderById(driverId, orderId);

    if (
      updateDto.status === MobileOrderStatus.Completed &&
      order.deliverySignatureRequired &&
      !updateDto.deliverySignature
    ) {
      throw new AppException(
        `Order requires delivery signature`,
        ErrorCode.ORDER_CUSTOMER_SIGNATURE_REQUIRED,
        HttpStatus.BAD_REQUEST,
      );
    }

    if (
      updateDto.status === MobileOrderStatus.PickedUp &&
      order.collectionSignatureRequired &&
      !updateDto.pickupSignature
    ) {
      throw new AppException(
        `Order requires pickup signature`,
        ErrorCode.ORDER_CUSTOMER_SIGNATURE_REQUIRED,
        HttpStatus.BAD_REQUEST,
      );
    }
    // Check if the status transition is valid
    const backendStatus = mapMobileToBackendStatus(updateDto.status);
    const currentBackendStatus = order.status;

    if (
      !this.orderStatusService.isValidTransition(
        currentBackendStatus,
        backendStatus,
      )
    ) {
      throw new AppException(
        `Invalid status transition from ${currentBackendStatus} to ${backendStatus}`,
        ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Update the order status via the order status service
    await this.ordersService.changeStatus(
      driver.tenantId,
      orderId,
      driverId,
      backendStatus,
      updateDto.statusNotes || '',
      '',
    );

    // Process the uploaded files and metadata
    // Get existing metadata to preserve any existing values
    const existingMetadata = (order as any).metadata || {};
    const metadata: Record<string, any> = { ...existingMetadata };
    let hasMetadataUpdates = false;

    // Store the detailed driver status in metadata
    switch (updateDto.status) {
      case MobileOrderStatus.GoingForPickup:
        metadata.driverStatus = 'going_for_pickup';
        metadata.driverStatusTimestamp = new Date().toISOString();
        hasMetadataUpdates = true;
        break;
      case MobileOrderStatus.PickedUp:
        metadata.driverStatus = 'picked_up';
        metadata.driverStatusTimestamp = new Date().toISOString();
        hasMetadataUpdates = true;
        break;
      case MobileOrderStatus.InTransit:
        metadata.driverStatus = 'going_for_delivery';
        metadata.driverStatusTimestamp = new Date().toISOString();
        hasMetadataUpdates = true;
        break;
      case MobileOrderStatus.Delivered:
        metadata.driverStatus = 'delivered';
        metadata.driverStatusTimestamp = new Date().toISOString();
        hasMetadataUpdates = true;
        break;
    }

    const fileMetaData = {
      orderType: this.determineOrderType(order.status),
    };

    // Handle proof of delivery image if provided
    if (updateDto.attachments && updateDto.attachments.length > 0) {
      await this.fileUploadService.uploadMultipleFiles(
        updateDto.attachments as Express.Multer.File[],
        'order',
        orderId,
        FILE_UPLOAD_CONSTANTS.FILE_ATTACHMENTS,
        driverId,
        fileMetaData,
      );
    }

    // Handle customer signature if provided
    if (
      updateDto.status === MobileOrderStatus.PickedUp &&
      updateDto.pickupSignature
    ) {
      await this.fileUploadService.uploadFile(
        updateDto.pickupSignature,
        'order',
        orderId,
        FILE_UPLOAD_CONSTANTS.COLLECTION_SIGNATURE,
        driverId,
      );
    }

    // Handle signature image if provided
    if (
      updateDto.status === MobileOrderStatus.Completed &&
      updateDto.deliverySignature
    ) {
      await this.fileUploadService.uploadFile(
        updateDto.deliverySignature,
        'order',
        orderId,
        FILE_UPLOAD_CONSTANTS.DELIVERY_SIGNATURE,
        driverId,
      );
    }

    // Update order metadata if there are any updates
    if (hasMetadataUpdates) {
      await this.ordersService.update(driver.tenantId, orderId, driverId, {
        metadata: metadata,
      });
    }

    // If the order is picked up, record the actual collection time if not already set
    if (
      updateDto.status === MobileOrderStatus.PickedUp &&
      !order.actualCollectionTime
    ) {
      await this.ordersService.update(driver.tenantId, orderId, driverId, {
        actualCollectionTime: new Date(),
      });
    }

    // If the order is delivered or completed, record the actual delivery time
    if (
      backendStatus === BackendOrderStatus.Completed ||
      updateDto.status === MobileOrderStatus.Delivered
    ) {
      await this.ordersService.update(driver.tenantId, orderId, driverId, {
        actualDeliveryTime: new Date(),
      });
    }

    // Return the updated order
    return this.ordersService.findOne(driver.tenantId, orderId);
  }

  async acceptOrder(driverId: string, orderId: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    try {
      const order = await this.getOrderById(orderId, driver.id, true);

      const includedStatusesForAcceptOrder = [
        MobileOrderStatus.Pending,
        MobileOrderStatus.Submitted,
      ];

      // Convert backend status to mobile status for comparison
      const mobileStatus = mapBackendToMobileStatus(order.status);
      if (!includedStatusesForAcceptOrder.includes(mobileStatus)) {
        throw new AppException(
          `Order with ID ${orderId} is not in Pending status`,
          ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
          HttpStatus.BAD_REQUEST,
        );
      }

      const activeSessionOfDriver =
        await this.timeClockSessionService.findCurrentSessionForDriver(
          driverId,
        );

      const vehicleId = activeSessionOfDriver?.vehicleId;

      // Assign the order to the driver and update the status
      await this.orderAssignmentService.assignOrder(
        orderId,
        driverId,
        driverId, // Driver is assigning themselves
        vehicleId,
        'Driver accepted order via mobile app',
      );

      // Return the updated order
      return this.ordersService.findOne(driver.tenantId, orderId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        `Order with ID ${orderId} not found`,
        ErrorCode.ORDER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async rejectOrder(driverId: string, orderId: string, reason: string) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    const order = await this.getDriverOrderById(driverId, orderId);

    // Convert backend status to mobile status for comparison
    const mobileStatus = mapBackendToMobileStatus(order.status);
    if (
      ![MobileOrderStatus.Pending, MobileOrderStatus.Assigned].includes(
        mobileStatus,
      )
    ) {
      throw new AppException(
        `Cannot reject an order that is already ${order.status}`,
        ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Unassign the order
    await this.orderAssignmentService.unassignOrder(
      orderId,
      driverId, // Driver is unassigning themselves
      reason,
    );

    return this.ordersService.findOne(driver.tenantId, orderId);
  }

  async transferOrder(
    driverId: string,
    orderId: string,
    newDriverId: string,
    reason: string,
  ) {
    // Get the driver to determine their tenant ID
    const driver = await this.usersService.findById(driverId);

    if (!driver) {
      throw new AppException(
        `Driver with ID ${driverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    const order = await this.getDriverOrderById(driverId, orderId);

    // Convert backend status to mobile status for comparison
    const mobileStatus = mapBackendToMobileStatus(order.status);
    if (
      ![MobileOrderStatus.Assigned, MobileOrderStatus.Pending].includes(
        mobileStatus,
      )
    ) {
      throw new AppException(
        `Cannot transfer an order that is already ${order.status}`,
        ErrorCode.ORDER_INVALID_STATUS_TRANSITION,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Verify the new driver exists
    const newDriver = await this.usersService.findById(newDriverId);
    if (!newDriver) {
      throw new AppException(
        `New driver with ID ${newDriverId} not found`,
        ErrorCode.USER_NOT_FOUND,
        HttpStatus.NOT_FOUND,
      );
    }

    // Verify both drivers belong to the same tenant
    if (driver.tenantId !== newDriver.tenantId) {
      throw new AppException(
        `Cannot transfer order to driver from different tenant`,
        ErrorCode.USER_INVALID_STATUS,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if the new driver is available
    // In a real implementation, you might check driver status, current load, etc.

    // Find the new driver's active vehicle
    const filter = new BaseFilterDto();
    filter.pageNumber = 1;
    filter.pageSize = 10;
    filter.where = {
      driverId: { [FilterOperator.EQ]: newDriverId },
      isActive: { [FilterOperator.EQ]: true },
    };
    const vehiclesResult = await this.vehiclesService.getVehicleList(
      filter,
      driver.tenantId,
    );
    const newVehicleId =
      vehiclesResult.data.length > 0 ? vehiclesResult.data[0].id : undefined;

    // If no active vehicle found and order seems to need a vehicle
    // Since we can't directly check vehicleTypeId in the DTO,
    // we'll make a determination based on other factors
    if (
      !newVehicleId &&
      (order.assignedVehicleId ||
        (order.totalWeight && order.totalWeight > 100))
    ) {
      throw new AppException(
        `New driver has no active vehicle, but this order likely requires one`,
        ErrorCode.VEHICLE_NOT_FOUND,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Reassign the order to the new driver
    await this.orderAssignmentService.reassignOrder(
      orderId,
      driverId, // Current driver is reassigning
      newDriverId,
      newVehicleId,
      reason || 'Driver requested transfer',
    );

    // Add transfer information to order notes
    await this.ordersService.update(driver.tenantId, orderId, driverId, {
      internalNotes: `Order transferred from driver ${driverId} to driver ${newDriverId}. Reason: ${reason}`,
    });

    // Return the updated order
    return this.ordersService.findOne(driver.tenantId, orderId);
  }

  determineOrderType(status: OrderStatus): OrderType {
    const deliveryStatuses: OrderStatus[] = [
      OrderStatus.PickedUp,
      OrderStatus.InTransit,
      OrderStatus.OnHold,
      OrderStatus.Delivered,
      OrderStatus.Completed,
      OrderStatus.Failed,
      OrderStatus.Returned,
    ];
    return deliveryStatuses.includes(status) ? 'Delivery' : 'Collection';
  }

  extractAddressFromOrder = (address: any) => ({
    id: address.id,
    name: address.name || '',
    companyName: address.companyName || '',
    email: address.email || '',
    countryCode: address.countryCode || '',
    phoneNumber: address.phoneNumber || '',
    phoneExtension: address.phoneExtension || '',
    notes: address.notes || '',
    addressLine1: address.addressLine1 || '',
    addressLine2: address.addressLine2 || '',
    city: address.city || '',
    province: address.province || '',
    postalCode: address.postalCode || '',
    country: address.country || '',
    latitude: address.latitude,
    longitude: address.longitude,
  });

  getStatusText = (backendStatus: BackendOrderStatus, order: any): string => {
    // Convert backend status to mobile status
    let mobileStatus = mapBackendToMobileStatus(backendStatus);
    // For InProgress status, get the detailed status from metadata
    if (mobileStatus === OrderStatus.InTransit) {
      mobileStatus = getDetailedInProgressStatus(order);
    }

    const statusMap = {
      [OrderStatus.Pending]: 'Pending',
      [OrderStatus.Assigned]: 'Assigned',
      [OrderStatus.GoingForPickup]: 'Going for Pickup',
      [OrderStatus.PickedUp]: 'Picked Up',
      [OrderStatus.InTransit]: 'In Transit',
      [OrderStatus.OnHold]: 'On Hold',
      [OrderStatus.Delivered]: 'Delivered',
      [OrderStatus.Failed]: 'Failed',
      [OrderStatus.Cancelled]: 'Cancelled',
      [OrderStatus.Returned]: 'Returned',
      [OrderStatus.Completed]: 'Completed',
    };
    return statusMap[mobileStatus] || mobileStatus.toString();
  };

  /**
   * Extract service options from order data
   * This replaces hardcoded service options with dynamic ones based on the order
   */
  private extractServiceOptions(order: any): { description: string }[] {
    const serviceOptions: { description: string }[] = [];
    // Get price modifiers from order metadata if available

    const pricingSummary = (order as any).pricingSummary;
    if (pricingSummary && pricingSummary.modifiers) {
      const serviceOptions = pricingSummary.modifiers.map((modifier: any) => ({
        description: modifier.name || modifier.description || 'Price modifier',
        amount: +modifier.amount || 0,
      }));

      serviceOptions.push({
        description: `basePrice`,
        amount: +pricingSummary.basePrice || 0,
      });
      return serviceOptions;
    }

    // If no price modifiers in metadata, extract from other order info
    // Check if there's a base price calculation
    if (order.basePrice) {
      // Convert to number if it's a string
      const basePrice =
        typeof order.basePrice === 'string'
          ? parseFloat(order.basePrice)
          : order.basePrice;

      serviceOptions.push({
        description: `Base price: ${Number(basePrice).toFixed(2)}`,
      });
    }

    // Check for misc adjustments
    if (order.miscAdjustment && order.miscAdjustment !== 0) {
      // Convert to number if it's a string
      const miscAdjustment =
        typeof order.miscAdjustment === 'string'
          ? parseFloat(order.miscAdjustment)
          : order.miscAdjustment;

      serviceOptions.push({
        description: `Adjustment: ${miscAdjustment > 0 ? '+' : ''}${Number(miscAdjustment).toFixed(2)}`,
      });
    }

    // If we still don't have any service options, provide minimal info
    if (serviceOptions.length === 0) {
      serviceOptions.push({
        description: 'Standard delivery service',
      });
    }

    return serviceOptions;
  }

  /**
   * Calculate dimensions based on order items
   */
  private calculateDimensions(items: any[] | undefined): string {
    if (!items || items.length === 0) {
      return 'N/A';
    }

    // Try to extract dimensions from items if they exist
    const itemsWithDimensions = items.filter(
      (item) => item.length && item.width && item.height,
    );

    if (itemsWithDimensions.length > 0) {
      // Use the largest item's dimensions as representative
      const largestItem = itemsWithDimensions.reduce((largest, item) => {
        const currentVolume = item.length * item.width * item.height;
        const largestVolume = largest.length * largest.width * largest.height;
        return currentVolume > largestVolume ? item : largest;
      }, itemsWithDimensions[0]);

      return `${largestItem.length} × ${largestItem.width} × ${largestItem.height} ${largestItem.dimensionUnit || ''}`;
    }

    // If no specific dimensions, return N/A
    return 'N/A';
  }

  mapToMobileOrderListItem(order: any): MobileOrderListItemDto {
    return {
      id: order.id,
      orderNumber: order.trackingNumber?.replace(/^(ORD-|TRK-)/, '') || '',
      customerName: order.customerName || 'Unknown Customer',
      companyName: order.companyName,
      type: this.determineOrderType(mapBackendToMobileStatus(order.status)),
      scheduledCollectionTime: formatDateTime(
        order.scheduledCollectionTime as Date,
        DEFAULT_DATETIME_FORMAT_12H,
      ),
      scheduledDeliveryTime: formatDateTime(
        order.scheduledDeliveryTime as Date,
        DEFAULT_DATETIME_FORMAT_12H,
      ),
      status: this.getStatusText(order.status, order) || 'Submitted',
      collectionLocation: order.collectionAddress as AddressDto,
      deliveryLocation: order.deliveryAddress as AddressDto,
      requestedBy: order.requestedByName || 'Unknown',
      recipientName: order.deliveryContactName,
      serviceLevel: order.serviceLevel,
      description: order.description,
      totalPrice: Number((+order.totalPrice || 0).toFixed(2)),
      quantity: Number(order.totalItems) || 0,
      weight: Number((+order.totalWeight || 0).toFixed(2)),
      dimensions: this.calculateDimensions(order.items),
      internalNotes: order.internalNotes,
    };
  }

  mapToOrderDetails(order: any): OrderDetailsResponseDto {
    return {
      id: order.id,
      orderNumber: order.trackingNumber?.replace(/^(ORD-|TRK-)/, '') || '',
      status: order.status,
      companyName: order.companyName || '',
      customerName: order.customerName || 'Unknown Customer',
      requestedBy: order.requestedByName || order.customerName || 'Unknown',
      collectionTime: order.scheduledCollectionTime
        ? formatDateTime(
            order.scheduledCollectionTime as Date,
            DEFAULT_DATETIME_FORMAT_12H,
          )
        : '',
      deliveryTime: order.scheduledDeliveryTime
        ? formatDateTime(
            order.scheduledDeliveryTime as Date,
            DEFAULT_DATETIME_FORMAT_12H,
          )
        : '',
      collectionLocation: order.collectionAddress as AddressDto,
      deliveryLocation: order.deliveryAddress as AddressDto,
      isCod: order.isCod || false,
      codAmount: order.codAmount || '',
      serviceLevel: order.serviceLevel || '',
      serviceOptions: this.extractServiceOptions(order),
      collectionSignatureRequired: order.collectionSignatureRequired || false,
      deliverySignatureRequired: order.deliverySignatureRequired || false,
      description: order.description || '',
      internalNotes: order.internalNotes || '',
      totalPrice: Number((+order.totalPrice || 0).toFixed(2)),
      quantity: Number(order.totalItems) || 0,
      weight: Number((+order.totalWeight || 0).toFixed(2)),
      dimensions: this.calculateDimensions(order.items),
      attachments: order.attachments,
    };
  }
}
