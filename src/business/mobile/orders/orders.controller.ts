import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  Request,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { MobileOrdersService } from './services/mobile-orders.service';
import { OrderStatus } from './domain/order';
import { UpdateOrderStatusDto } from './dto/update-order-status.dto';
import { OrderDetailsResponseDto } from './dto/order-details-response.dto';
import { MobileOrderListItemDto } from '@app/business/mobile/orders/dto/order-list-response.dto';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { DriverService } from '../../user/drivers/driver.service';

@ApiTags('Mobile - Orders')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller({
  path: 'mobile/orders',
  version: '1',
})
export class MobileOrdersController {
  constructor(
    private readonly ordersService: MobileOrdersService,
    private readonly driverService: DriverService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get orders for the authenticated driver' })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: OrderStatus,
    isArray: true,
    description: 'Filter orders by status',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of orders retrieved successfully',
    type: [MobileOrderListItemDto],
  })
  async getDriverOrders(
    @Request() req: any,
    @Query('status') status?: OrderStatus | OrderStatus[],
  ) {
    const driverId = req.user.sub;
    const orders = await this.ordersService.getDriverOrders(driverId, status);

    // Transform orders into the format shown in the screenshot with pickup and delivery pairs
    const formattedOrders: MobileOrderListItemDto[] = [];

    orders.forEach((order) => {
      formattedOrders.push(this.ordersService.mapToMobileOrderListItem(order));
    });
    return formattedOrders;
  }

  @Get('unassigned')
  @ApiOperation({ summary: 'Get all unassigned orders for the tenant' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of unassigned orders retrieved successfully',
  })
  async getUnassignedOrders(@Req() req: any) {
    const unassignedOrders = await this.ordersService.getUnassignedOrders(
      req.user.sub,
    );
    const formattedOrders: MobileOrderListItemDto[] = [];

    unassignedOrders.forEach((order) => {
      formattedOrders.push(this.ordersService.mapToMobileOrderListItem(order));
    });

    return formattedOrders;
  }

  @Get('unassigned/:id')
  @ApiOperation({ summary: 'Get unassigned order details by ID' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Unassigned order details retrieved successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  async getUnassignedOrderDetails(
    @Request() req: any,
    @Param('id') id: string,
  ) {
    const driverId = req.user.sub;
    const order = await this.ordersService.getOrderById(id, driverId, true);

    // Format order details according to the UI
    const response: OrderDetailsResponseDto =
      this.ordersService.mapToOrderDetails(order);

    return response;
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get order details by ID (Assigned order)' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order details retrieved successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  async getOrderDetails(@Request() req: any, @Param('id') id: string) {
    const driverId = req.user.sub;
    const order = await this.ordersService.getDriverOrderById(driverId, id);

    const response: OrderDetailsResponseDto =
      this.ordersService.mapToOrderDetails(order);

    return response;
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update order status' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'attachments' },
      { name: 'pickupSignature', maxCount: 1 },
      { name: 'deliverySignature', maxCount: 1 },
    ]),
  )
  @ApiBody({ type: UpdateOrderStatusDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order status updated successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Invalid status transition' })
  async updateOrderStatus(
    @Request() req: any,
    @Param('id') orderId: string,
    @Body() updateDto: UpdateOrderStatusDto,
    @UploadedFiles()
    files: {
      attachments?: Express.Multer.File[];
      pickupSignature?: Express.Multer.File;
      deliverySignature?: Express.Multer.File;
    },
  ) {
    const driverId = req.user.sub;

    // Merge DTO with uploaded files
    const updateDtoWithFiles: UpdateOrderStatusDto = {
      ...updateDto,
      attachments: files?.attachments,
      pickupSignature: files?.pickupSignature?.[0],
      deliverySignature: files?.deliverySignature?.[0],
    };

    await this.ordersService.updateOrderStatus(
      driverId,
      orderId,
      updateDtoWithFiles,
    );

    // Return updated order details
    return this.getOrderDetails(req, orderId);
  }

  @Post(':id/accept')
  @ApiOperation({ summary: 'Accept an order' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order accepted successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiBadRequestResponse({ description: 'Order cannot be accepted' })
  async acceptOrder(@Request() req: any, @Param('id') id: string) {
    const driverId = req.user.sub;
    await this.ordersService.acceptOrder(driverId, id);
    return this.getOrderDetails(req, id); // Return the full order details
  }

  @Post(':id/reject')
  @ApiOperation({ summary: 'Reject an order' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        reason: { type: 'string', example: 'Vehicle issue' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order rejected successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Order cannot be rejected' })
  async rejectOrder(
    @Request() req: any,
    @Param('id') id: string,
    @Body('reason') reason: string,
  ) {
    const driverId = req.user.sub;
    const rejectedOrder = await this.ordersService.rejectOrder(
      driverId,
      id,
      reason,
    );
    return this.ordersService.mapToOrderDetails(rejectedOrder); // Return the full order details
  }

  @Get('drivers')
  @ApiOperation({ summary: 'Get all drivers for the minimal' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of drivers retrieved successfully',
  })
  async getDrivers(@Request() req: any) {
    const drivers = await this.driverService.getAllDrivers(req.user.sub);
    return drivers;
  }

  @Post(':id/transfer')
  @ApiOperation({ summary: 'Transfer an order to another driver' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        newDriverId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440002',
        },
        reason: { type: 'string', example: 'Driver unavailable' },
      },
      required: ['newDriverId'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Order transferred successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({ description: 'Order not assigned to driver' })
  @ApiBadRequestResponse({ description: 'Order cannot be transferred' })
  async transferOrder(
    @Request() req: any,
    @Param('id') id: string,
    @Body('newDriverId') newDriverId: string,
    @Body('reason') reason: string,
  ) {
    const driverId = req.user.sub;
    await this.ordersService.transferOrder(
      driverId,
      id,
      newDriverId,
      reason || 'Driver requested transfer',
    );
    return this.getOrderDetails(req, id); // Return the full order details
  }

  /**
   * Determine the delivery type based on scheduled collection and delivery times
   * This replaces hardcoded delivery type logic with a consistent service-level function
   */
  private determineDeliveryType(
    scheduledCollectionTime: Date | string | undefined,
    scheduledDeliveryTime: Date | string | undefined,
    metadataServiceLevel?: string,
  ): string {
    // First check if there's an explicit service level in metadata or from the database
    if (metadataServiceLevel) {
      return metadataServiceLevel;
    }

    // If both times are provided, determine based on date comparison
    if (scheduledCollectionTime && scheduledDeliveryTime) {
      const pickupDate = new Date(scheduledCollectionTime);
      const deliveryDate = new Date(scheduledDeliveryTime);

      const isSameDay =
        pickupDate.getDate() === deliveryDate.getDate() &&
        pickupDate.getMonth() === deliveryDate.getMonth() &&
        pickupDate.getFullYear() === deliveryDate.getFullYear();

      // Calculate hours difference to determine service level
      const hoursDiff =
        (deliveryDate.getTime() - pickupDate.getTime()) / (1000 * 60 * 60);

      // Determine service level based on time difference
      if (isSameDay) {
        if (hoursDiff <= 2) return 'Express';
        if (hoursDiff <= 4) return 'Priority';
        return 'Sameday';
      } else {
        if (hoursDiff <= 24) return 'Overnight';
        if (hoursDiff <= 48) return 'Nextday';
        return 'Standard';
      }
    }

    // Default to Standard if we can't determine
    return 'Standard';
  }
}
