import { IsEnum, IsOptional, IsString, ValidateIf } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderStatus } from '@app/business/mobile/orders/domain/order';

export class UpdateOrderStatusDto {
  @ApiProperty({ enum: OrderStatus })
  @IsEnum(OrderStatus)
  status: OrderStatus;

  @ApiPropertyOptional({ example: '05:20:00' })
  @IsString()
  @IsOptional()
  waitTime?: string;

  @ApiPropertyOptional({ example: 'Customer not available' })
  @IsString()
  @IsOptional()
  statusNotes?: string;

  @ApiPropertyOptional({ type: 'string', format: 'binary' })
  @IsOptional()
  attachments?: Express.Multer.File[];

  @ApiPropertyOptional({ type: 'string', format: 'binary' })
  @IsString()
  @IsOptional()
  @ValidateIf((o) => o.status === OrderStatus.PickedUp)
  pickupSignature?: Express.Multer.File;

  @ApiPropertyOptional({ type: 'string', format: 'binary' })
  @IsOptional()
  @ValidateIf((o) => o.status === OrderStatus.Delivered)
  deliverySignature?: Express.Multer.File;
}
