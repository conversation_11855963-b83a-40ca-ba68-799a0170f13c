import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { DriverLoginDto } from './dto/login.dto';
import { DriverRepository } from '../../user/drivers/infrastructure/repository/driver.repository';
import { compare } from 'bcryptjs';
import { ConfigService } from '@nestjs/config';
import { TenantRepository } from '../../user/tenants/infrastructure/persistence/tenant.repository';
import {
  DriverAuthResponseDto,
  DriverProfileResponseDto,
} from './dto/auth-response.dto';
import {
  DriverLoginException,
  DriverNotFoundException,
  DriverInactiveException,
} from '@utils/errors/exceptions/driver.exceptions';
import { TenantNotFoundException } from '@utils/errors/exceptions/tenant.exceptions';
import { TokenExpiredException } from '@utils/errors/exceptions/auth.exceptions';

@Injectable()
export class MobileAuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly driverRepository: DriverRepository,
    private readonly tenantRepository: TenantRepository,
  ) {}

  async login(loginDto: DriverLoginDto): Promise<DriverAuthResponseDto> {
    const driver = await this.driverRepository.findOne({
      email: loginDto.email,
    });
    if (!driver) {
      throw new DriverLoginException();
    }

    const validPassword = await compare(loginDto.password, driver.password);
    if (!validPassword) {
      throw new DriverLoginException();
    }

    if (driver.status !== 'Active') {
      throw new DriverInactiveException(loginDto.email);
    }

    const tenant = await this.tenantRepository.findOne({
      where: { id: driver.tenantId },
    });
    if (!tenant) {
      throw new TenantNotFoundException(driver.tenantId);
    }

    const payload = {
      sub: driver.id,
      email: driver.email,
      name: driver.name,
      role: 'driver',
    };

    const secret = this.configService.get<string>(
      'auth.mobileJwtSecret',
      'mobile-driver-jwt-secret',
      { infer: true },
    );

    const expiresIn = this.configService.get<string>(
      'auth.mobileJwtExpiresIn',
      '24h',
      { infer: true },
    );

    const accessToken = this.jwtService.sign(payload, { secret, expiresIn });
    const refreshToken = this.generateRefreshToken(payload);

    const response: DriverAuthResponseDto = {
      id: driver.id,
      fullName: driver.name,
      email: driver.email,
      phoneNumber: driver.phoneNumber,
      status: driver.status,
      driverStatus: driver.driverStatus,
      companyName: tenant?.name,
      token: {
        accessToken,
        refreshToken,
        expiresIn: parseInt(expiresIn, 10),
      },
    };
    return response;
  }

  async findDriverById(id: string): Promise<DriverProfileResponseDto> {
    const driver = await this.driverRepository.findOne({ id });
    if (!driver) {
      throw new DriverNotFoundException(id);
    }

    const tenant = await this.tenantRepository.findOne({
      where: { id: driver.tenantId },
    });
    if (!tenant) {
      throw new TenantNotFoundException(driver.tenantId);
    }

    const response: DriverProfileResponseDto = {
      id: driver.id,
      fullName: driver.name,
      email: driver.email,
      companyName: tenant.name,
      phoneNumber: driver.phoneNumber,
      status: driver.status,
      driverStatus: driver.driverStatus,
    };
    return response;
  }

  async refreshAccessToken(refreshToken: string) {
    try {
      const refreshSecret = this.configService.get<string>(
        'auth.mobileJwtRefreshSecret',
        'mobile-driver-jwt-refresh-secret',
        { infer: true },
      );

      const payload = this.jwtService.verify(refreshToken, {
        secret: refreshSecret,
      });

      const driver = await this.findDriverById(payload.sub);
      if (!driver) {
        throw new DriverNotFoundException(payload.sub);
      }

      const newPayload = {
        sub: driver.id,
        email: driver.email,
        name: driver.fullName,
        role: 'driver',
      };

      const secret = this.configService.get<string>(
        'auth.mobileJwtSecret',
        'mobile-driver-jwt-secret',
        { infer: true },
      );

      const expiresIn = this.configService.get<string>(
        'auth.mobileJwtExpiresIn',
        '24h',
        { infer: true },
      );

      return {
        accessToken: this.jwtService.sign(newPayload, { secret, expiresIn }),
        refreshToken: this.generateRefreshToken(newPayload),
        expiresIn: parseInt(expiresIn, 10),
      };
    } catch {
      throw new TokenExpiredException('refresh');
    }
  }

  private generateRefreshToken(payload: any): string {
    const secret = this.configService.get<string>(
      'auth.mobileJwtRefreshSecret',
      'mobile-driver-jwt-refresh-secret',
      { infer: true },
    );

    const expiresIn = this.configService.get<string>(
      'auth.mobileJwtRefreshExpiresIn',
      '7d',
      { infer: true },
    );

    return this.jwtService.sign(payload, {
      secret,
      expiresIn,
    });
  }
}
