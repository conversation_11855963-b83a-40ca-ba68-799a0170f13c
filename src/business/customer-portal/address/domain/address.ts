import { AutoMap } from '@automapper/classes';
import { UserEntity } from '../../../user/users/infrastructure/entities/user.entity';

export class AddressDomain {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  customer: UserEntity;

  @AutoMap()
  customerId: string;

  @AutoMap()
  name: string;

  @AutoMap()
  companyName: string;

  @AutoMap()
  email: string;

  @AutoMap()
  countryCode: string;

  @AutoMap()
  phoneNumber: string;

  @AutoMap()
  phoneExtension: string;

  @AutoMap()
  addressLine1: string;

  @AutoMap()
  addressLine2: string;

  @AutoMap()
  city: string;

  @AutoMap()
  province: string;

  @AutoMap()
  postalCode: string;

  @AutoMap()
  country: string;

  @AutoMap()
  notes: string;

  @AutoMap()
  latitude: number;

  @AutoMap()
  longitude: number;

  @AutoMap()
  isFavoriteForPickup: boolean;

  @AutoMap()
  isFavoriteForDelivery: boolean;

  @AutoMap()
  isDefaultForPickup: boolean;

  @AutoMap()
  isDefaultForDelivery: boolean;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy: string;
}
