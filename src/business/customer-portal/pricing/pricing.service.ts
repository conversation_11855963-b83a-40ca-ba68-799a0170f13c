import { Injectable } from '@nestjs/common';
import { PriceSetsService } from '@app/business/pricing/price-sets/price-sets.service';
import { PriceCalculatorService } from '@core/pricing/price-calculator.service';
import {
  AvailableServiceDto,
  GetAvailableServicesRequestDto,
} from './dto/get-available-services.dto';
import { IOrder } from '@core/pricing/domain/order.interface';
import { IPriceModifier } from '@core/pricing/domain/price-modifier.interface';
import { toTenantTimezone } from '@app/business/pricing/utils/date.utils';
import { AddressService } from '../../customer-portal/address/address.service';
import { ZoneRepository } from '../../zone/zones/infrastructure/repositories/zone.repository';
import { DistanceUnit } from '../../order/orders/domain/order.types';
import { calculateAddressDistanceById } from '../../../utils/distance.utils';

@Injectable()
export class PricingService {
  constructor(
    private readonly priceSetService: PriceSetsService,
    private readonly priceCalculatorService: PriceCalculatorService,
    private readonly address: AddressService,
    private readonly zoneRepository: ZoneRepository,
  ) {}

  /**
   * Get available services based on pickup date with optional pricing
   */
  async getAvailableServices(
    params: GetAvailableServicesRequestDto,
    tenantId: string,
  ): Promise<AvailableServiceDto[]> {
    // Get available price sets based on pickup date
    // Convert to tenant timezone to ensure consistent handling
    const pickupDate = toTenantTimezone(params.pickupDate).toDate();
    if (params.order?.collectionAddressId && params.order?.deliveryAddressId) {
      const collectionAddress = await this.address.getAddressDetails(
        params.order.collectionAddressId,
      );
      const deliveryAddress = await this.address.getAddressDetails(
        params.order.deliveryAddressId,
      );

      const calculatedDistance = await calculateAddressDistanceById(
        this.address,
        params.order.collectionAddressId,
        params.order.deliveryAddressId,
        DistanceUnit.Kilometers,
        collectionAddress?.latitude,
        collectionAddress?.longitude,
        deliveryAddress?.latitude,
        deliveryAddress?.longitude,
      );

      if (calculatedDistance > 0) {
        params.order.distance = calculatedDistance;
      }
    }
    const availablePriceSets =
      await this.priceSetService.getPriceSetsByPickupDate(pickupDate, tenantId);

    // Calculate prices if requested
    if (params.includePricing && params.order) {
      await this.addPricingToServices(
        availablePriceSets,
        params.order,
        tenantId,
      );
    }

    return availablePriceSets;
  }

  /**
   * Add pricing information to the available services
   */
  private async addPricingToServices(
    services: AvailableServiceDto[],
    order: IOrder,
    tenantId: string,
  ): Promise<void> {
    for (const service of services) {
      try {
        // Get modifiers for this price set
        const priceModifiers =
          await this.priceSetService.getCompletePriceSetModifiers(service.id);

        // Get zone table for this price set
        await this.priceSetService.getBasePriceByZone(service.id);

        // Get addresses
        const collectionAddress = await this.address.getAddressDetails(
          order.collectionAddressId,
        );
        const deliveryAddress = await this.address.getAddressDetails(
          order.deliveryAddressId,
        );

        // Get postal codes from addresses
        const collectionPostalCode = collectionAddress.postalCode;
        const deliveryPostalCode = deliveryAddress.postalCode;

        const collectionZonePrefix = collectionPostalCode
          ?.trim()
          .substring(0, 3)
          .toUpperCase();
        const deliveryZonePrefix = deliveryPostalCode
          ?.trim()
          .substring(0, 3)
          .toUpperCase();

        // Get zones based on postal codes
        const originZone =
          await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
            collectionZonePrefix,
            tenantId,
          );
        const destinationZone =
          await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
            deliveryZonePrefix,
            tenantId,
          );

        // Update order with zone IDs if zones were found
        if (originZone && destinationZone) {
          order.originZoneId = originZone.id;
          order.destinationZoneId = destinationZone.id;
        }

        // Convert to IPriceModifier format for calculator
        const modifiersForCalculation: IPriceModifier[] =
          this.mapPriceModifiers(priceModifiers);

        // Create order object with base price from zone if applicable
        const orderWithBasePrice = await this.enrichOrderWithBasePrice(
          order,
          service.id,
        );

        // Calculate price
        const calculationResult = this.priceCalculatorService.calculatePrice(
          orderWithBasePrice,
          modifiersForCalculation,
        );

        const requiredModifierPrice = calculationResult.modifiers
          .filter((mod) => mod.configuration === 'required')
          .reduce((sum, mod) => sum + mod.amount, 0);

        const selectedModifierPrice = calculationResult.modifiers
          .filter((mod) => mod.configuration === 'selected')
          .reduce((sum, mod) => sum + mod.amount, 0);

        // Add to service response
        service.pricing = {
          basePrice: calculationResult.basePrice,
          totalPrice: calculationResult.totalPrice,
          totalModifierPrice: calculationResult.modifiers.reduce(
            (sum, mod) => sum + mod.amount,
            0,
          ),
          requiredTotalPrice:
            calculationResult.basePrice + requiredModifierPrice,
          selectedModifierPrice,
          modifiers: calculationResult.modifiers.map((modifier) => ({
            name: modifier.name,
            amount: modifier.amount,
            configuration: modifier.configuration,
            id: modifier.id,
          })),
        };
      } catch {
        // If there's an error calculating price, skip pricing for this service
        continue;
      }
    }
  }

  /**
   * Map the price modifiers from the repository format to the calculator format
   */
  private mapPriceModifiers(priceModifiers: any[]): IPriceModifier[] {
    return priceModifiers.map((modifier) => ({
      id: modifier.id,
      name: modifier.name,
      calculationType: modifier.calculationType,
      calculationField: modifier.calculationField,
      configuration: modifier.configuration,
      value: modifier.value,
      increment: modifier.increment,
      calculationStartAfter: modifier.calculationStartAfter,
      applicableRange: modifier.applicableRange,
      tieredRanges: modifier.tieredRanges,
      isGroupModifier: modifier.isGroupModifier || false,
      modifiers: modifier.childModifiers
        ? this.mapPriceModifiers(modifier.childModifiers)
        : undefined,
      groupBehavior: modifier.groupBehavior,
      isEnabled: modifier.isEnabled !== false,
    }));
  }

  /**
   * Add base price to the order based on zones if applicable
   */
  private async enrichOrderWithBasePrice(
    order: IOrder,
    priceSetId: string,
  ): Promise<IOrder> {
    try {
      // If the order already has a base price, use it
      if (order.basePrice && order.basePrice > 0) {
        return order;
      }
      // Try to get zone-based pricing
      if (order.collectionAddressId && order.deliveryAddressId) {
        const zoneTable =
          await this.priceSetService.getBasePriceByZone(priceSetId);
        // Find matching zone value
        const zoneValue = zoneTable.zoneTableValues?.find((value) => {
          return (
            value.originZoneId === order.originZoneId &&
            value.destinationZoneId === order.destinationZoneId
          );
        });

        if (zoneValue && zoneValue.value) {
          return {
            ...order,
            basePrice: zoneValue.value,
          };
        }
      }
      // If no base price found, use a default
      return {
        ...order,
        basePrice: order.basePrice || 0,
      };
    } catch {
      // If there's an error getting zone-based pricing, use the original order
      return order;
    }
  }
}
