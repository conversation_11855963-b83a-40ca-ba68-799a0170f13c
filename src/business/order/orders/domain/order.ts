import { AutoMap } from '@automapper/classes';
import {
  OrderStatus,
  BillingStatus,
  PaymentStatus,
  DistanceUnit,
} from './order.types';

export class Order {
  @AutoMap()
  id: string;

  @AutoMap()
  tenantId: string;

  @AutoMap()
  trackingNumber: string;

  @AutoMap()
  referenceNumber?: string;

  @AutoMap()
  status: OrderStatus;

  // Customer Information
  @AutoMap()
  customerId: string;

  @AutoMap()
  customerName: string;

  @AutoMap()
  customerEmail?: string;

  @AutoMap()
  customerPhoneNumber?: string;

  @AutoMap()
  companyName?: string;

  @AutoMap()
  customerContactName?: string;

  @AutoMap()
  requestedById: string;

  @AutoMap()
  requestedByName?: string;

  @AutoMap()
  collectionEmail?: string;

  @AutoMap()
  collectionPhone?: string;

  @AutoMap()
  collectionPhoneExtension?: string;

  @AutoMap()
  submittedById: string;

  // Package Template Info
  @AutoMap()
  packageTemplateId?: string;

  // Collection Information
  @AutoMap()
  collectionAddressId: string;

  @AutoMap()
  collectionContactName?: string;

  @AutoMap()
  collectionInstructions?: string;

  @AutoMap()
  collectionAddress?: string;

  @AutoMap()
  collectionCompanyName?: string;

  @AutoMap()
  collectionSignatureRequired: boolean;

  @AutoMap()
  collectionSignatureData?: Record<string, any>;

  @AutoMap()
  scheduledCollectionTime?: Date;

  @AutoMap()
  actualCollectionTime?: Date;

  @AutoMap()
  collectionZoneId?: string;

  @AutoMap()
  collectionZoneName?: string;

  // Delivery Information
  @AutoMap()
  deliveryAddressId: string;

  @AutoMap()
  deliveryContactName?: string;

  @AutoMap()
  deliveryCompanyName?: string;

  @AutoMap()
  deliveryInstructions?: string;

  @AutoMap()
  deliverySignatureRequired: boolean;

  @AutoMap()
  deliveryAddress?: string;

  @AutoMap()
  deliveryZoneName?: string;

  @AutoMap()
  deliverySignatureData?: Record<string, any>;

  @AutoMap()
  scheduledDeliveryTime?: Date;

  @AutoMap()
  actualDeliveryTime?: Date;

  @AutoMap()
  deliveryZoneId?: string;

  @AutoMap()
  deliveryEmail?: string;

  @AutoMap()
  deliveryPhone?: string;

  @AutoMap()
  deliveryPhoneExtension?: string;

  // Package Information
  @AutoMap()
  totalItems: number;

  @AutoMap()
  totalWeight?: number;

  @AutoMap()
  totalVolume?: number;

  @AutoMap()
  isInsurance: boolean;

  @AutoMap()
  declaredValue?: number;

  // Vehicle and Assignment Information
  @AutoMap()
  vehicleTypeId?: string;

  @AutoMap()
  vehicleTypeName?: string;

  @AutoMap()
  assignedDriverId?: string;

  @AutoMap()
  assignedDriverName?: string;

  @AutoMap()
  assignedVehicleId?: string;

  @AutoMap()
  assignedVehicleDescription?: string;

  @AutoMap()
  assignedVehicleName?: string;

  @AutoMap()
  isCod: boolean;

  @AutoMap()
  codAmount?: number;

  @AutoMap()
  codCollected: boolean;

  @AutoMap()
  codCollectionDate?: Date;

  // Pricing Information
  @AutoMap()
  priceSetId?: string;

  @AutoMap()
  priceSet?: string;

  @AutoMap()
  priceSetInternalName?: string;

  @AutoMap()
  basePriceType?: string;

  @AutoMap()
  basePrice: number;

  @AutoMap()
  optionsPrice: number;

  @AutoMap()
  miscAdjustment: number;

  @AutoMap()
  customerAdjustment: number;

  @AutoMap()
  totalPrice: number;

  // Billing Information
  @AutoMap()
  billingStatus: BillingStatus;

  @AutoMap()
  paymentStatus: PaymentStatus;

  // Stats/Metrics
  @AutoMap()
  distance?: number;

  @AutoMap()
  distanceUnit: DistanceUnit;

  @AutoMap()
  estimatedDuration?: string; // Stored as interval in DB

  @AutoMap()
  actualDuration?: string; // Stored as interval in DB

  // Additional Fields
  @AutoMap()
  description?: string;

  @AutoMap()
  comments?: string;

  @AutoMap()
  internalNotes?: string;

  @AutoMap()
  invoiceNumber?: string;

  @AutoMap()
  customFields?: Record<string, any>;

  @AutoMap()
  metadata?: Record<string, any>;

  // System Fields
  @AutoMap()
  isLocked: boolean;

  @AutoMap()
  lockedBy?: string;

  @AutoMap()
  lockReason?: string;

  @AutoMap()
  lockTimestamp?: Date;

  @AutoMap()
  pricingSummary?: any;

  @AutoMap()
  customPricingSummary?: Record<string, any>;

  @AutoMap()
  customTotalPrice?: number;

  @AutoMap()
  isDeleted: boolean;

  @AutoMap()
  deletedAt?: Date;

  @AutoMap()
  createdAt: Date;

  @AutoMap()
  updatedAt: Date;

  @AutoMap()
  createdBy: string;

  @AutoMap()
  updatedBy?: string;
}
