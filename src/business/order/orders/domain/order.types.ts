export enum OrderStatus {
  Draft = 'Draft',
  Submitted = 'Submitted',
  Pending = 'Pending',
  Assigned = 'Assigned',
  GoingForPickup = 'GoingForPickup',
  PickedUp = 'PickedUp',
  GoingForDelivery = 'GoingForDelivery',
  InTransit = 'InTransit',
  Completed = 'Completed',
  Cancelled = 'Cancelled',
}

export enum BillingStatus {
  NotBilled = 'Not_Billed',
  Billed = 'Billed',
  PartiallyBilled = 'Partially_Billed',
}

export enum PaymentStatus {
  Pending = 'Pending',
  Paid = 'Paid',
  PartiallyPaid = 'Partially_Paid',
}

export enum OrderStopType {
  Collection = 'Collection',
  Delivery = 'Delivery',
  Return = 'Return',
}

export enum DistanceUnit {
  Kilometers = 'km',
  Miles = 'mi',
}

export enum CODStatus {
  Required = 'Required',
  NotRequired = 'Not_Required',
  Collected = 'Collected',
}
