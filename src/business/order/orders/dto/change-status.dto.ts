import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { OrderStatus } from '../domain/order.types';

export class ChangeStatusDto {
  @ApiProperty({
    enum: OrderStatus,
    description: 'New status to set for the order',
    example: OrderStatus.InTransit,
  })
  @IsEnum(OrderStatus)
  @IsNotEmpty()
  status: OrderStatus;

  @ApiPropertyOptional({
    example: 'Customer requested cancellation',
    description: 'Reason for status change',
  })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiPropertyOptional({
    example: 'Customer called to cancel due to change of plans',
    description: 'Additional comments on the status change',
  })
  @IsString()
  @IsOptional()
  comments?: string;

  @ApiPropertyOptional({
    example: {
      latitude: 40.7128,
      longitude: -74.006,
      accuracy: 10,
    },
    description: 'Location data at the time of status change',
  })
  @IsObject()
  @IsOptional()
  locationData?: Record<string, any>;
}
