import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  OrderStatus,
  BillingStatus,
  PaymentStatus,
  DistanceUnit,
} from '../domain/order.types';
import { OrderItemResponseDto } from './order-item-response.dto';
export class OrderResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique identifier',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655441111',
    description: 'Tenant ID',
  })
  tenantId: string;

  @ApiProperty({
    example: {
      pickupSignature: 'https://example.com/pickup-sign.png',
      deliverySignature: 'https://example.com/delivery-sign.png',
      images: [
        'https://example.com/image1.jpg',
        'https://example.com/image2.png',
      ],
    },
  })
  attachments: OrderAttachments;

  @ApiProperty({
    example: 'TRK-20250408-AB12C',
    description: 'Unique tracking number for the order',
  })
  trackingNumber: string;

  @ApiPropertyOptional({
    example: 'REF-12345',
    description: 'Optional reference number (customer supplied)',
  })
  referenceNumber?: string;

  @ApiProperty({
    enum: OrderStatus,
    example: OrderStatus.Draft,
    description: 'Current order status',
  })
  status: OrderStatus;

  // Customer Information
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'Customer ID',
  })
  customerId: string;

  @ApiPropertyOptional({
    example: 'Acme Corporation',
    description: 'Customer company name',
  })
  customerName?: string;

  @ApiPropertyOptional({
    example: 'Acme Corporation',
    description: 'Customer company name',
  })
  companyName?: string;

  @ApiPropertyOptional({
    example: 'John Doe',
    description: 'Customer contact name',
  })
  customerContactName?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Customer Email',
  })
  customerEmail?: string;

  @ApiPropertyOptional({
    example: '******-123-4567',
    description: 'Customer Phone Number',
  })
  customerPhoneNumber?: string;

  @ApiPropertyOptional({
    example: 'INV-15452123',
    description: 'Invoice Number',
  })
  invoiceNumber?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655443333',
    description: 'Contact who requested the order',
  })
  requestedById: string;

  @ApiPropertyOptional({
    example: 'John Doe',
    description: 'Name of contact who requested the order',
  })
  requestedByName?: string;

  // Collection Information
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655444444',
    description: 'Collection address ID',
  })
  collectionAddressId: string;

  @ApiPropertyOptional({
    example: '123 Main St, New York, NY 10001',
    description: 'Collection address summary',
  })
  collectionAddressSummary?: string;

  @ApiPropertyOptional({
    example: 'John Smith',
    description: 'Contact name for collection',
  })
  collectionContactName?: string;

  @ApiPropertyOptional({
    example: '123 Main St, New York, NY 10001',
    description: 'Full collection address (if different from summary)',
  })
  collectionAddress?: string;

  @ApiPropertyOptional({
    example: 'Zone A',
    description: 'Collection zone name',
  })
  collectionZoneName?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655445555',
    description: 'Collection zone ID',
  })
  collectionZoneId?: string;

  @ApiPropertyOptional({
    example: 'Ring doorbell and wait for security',
    description: 'Special instructions for collection',
  })
  collectionInstructions?: string;

  @ApiProperty({
    example: false,
    description: 'Whether signature is required for collection',
  })
  collectionSignatureRequired: boolean;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Collection email',
  })
  collectionEmail?: string;

  @ApiPropertyOptional({
    example: '******-123-4567',
    description: 'Collection phone number',
  })
  collectionPhone?: string;

  @ApiPropertyOptional({
    example: 123,
    description: 'Collection phone extension',
  })
  collectionPhoneExtension?: string;

  @ApiProperty({
    example: 'Zone B',
    description: 'Delivery zone name',
  })
  deliveryZoneName?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655446666',
    description: 'Delivery zone ID',
  })
  deliveryZoneId?: string;

  @ApiPropertyOptional({
    example: '12 , Main St, New York, NY 10001',
    description: 'Delivery address ',
  })
  deliveryAddress?: string;

  @ApiProperty({
    example: false,
    description: 'Whether Order is locked',
  })
  isLocked: boolean;

  @ApiProperty({
    example: false,
    description: 'COD',
  })
  codCollected: boolean;

  @ApiPropertyOptional({
    example: '2025-04-10T09:00:00Z',
    description: 'Scheduled collection time',
  })
  scheduledCollectionTime?: Date;

  @ApiPropertyOptional({
    example: '2025-04-10T09:15:30Z',
    description: 'Actual collection time (when picked up)',
  })
  actualCollectionTime?: Date;

  // Delivery Information
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655455555',
    description: 'Delivery address ID',
  })
  deliveryAddressId: string;

  @ApiPropertyOptional({
    example: '456 Park Ave, New York, NY 10022',
    description: 'Delivery address summary',
  })
  deliveryAddressSummary?: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Contact name for delivery',
  })
  deliveryContactName?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Delivery email',
  })
  deliveryEmail?: string;

  @ApiPropertyOptional({
    example: '******-123-4567',
    description: 'delivery phone number',
  })
  deliveryPhone?: string;

  @ApiPropertyOptional({
    example: 123,
    description: 'delivery phone extension',
  })
  deliveryPhoneExtension?: string;

  @ApiPropertyOptional({
    example: 'Leave at front desk if no answer',
    description: 'Special instructions for delivery',
  })
  deliveryInstructions?: string;

  @ApiProperty({
    example: true,
    description: 'Whether signature is required for delivery',
  })
  deliverySignatureRequired: boolean;

  @ApiPropertyOptional({
    example: '2025-04-10T14:00:00Z',
    description: 'Scheduled delivery time',
  })
  scheduledDeliveryTime?: Date;

  @ApiPropertyOptional({
    example: '2025-04-10T13:45:20Z',
    description: 'Actual delivery time (when delivered)',
  })
  actualDeliveryTime?: Date;

  @ApiProperty({
    example: 3,
    description: 'Total number of items',
  })
  totalItems: number;

  @ApiPropertyOptional({
    example: 12.5,
    description: 'Total weight of all items',
  })
  totalWeight?: number;

  @ApiPropertyOptional({
    example: 0.5,
    description: 'Total volume of all items in cubic units',
  })
  totalVolume?: number;

  @ApiProperty({
    example: false,
    description: 'Whether insurance is required',
  })
  isInsurance: boolean;

  @ApiPropertyOptional({
    example: 500,
    description: 'Declared value of the shipment',
  })
  declaredValue?: number;

  // Vehicle and Assignment Information
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655477777',
    description: 'Assigned driver ID',
  })
  assignedDriverId?: string;

  @ApiPropertyOptional({
    example: 'John Smith',
    description: 'Assigned driver name',
  })
  assignedDriverName?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655488888',
    description: 'Assigned vehicle ID',
  })
  assignedVehicleId?: string;

  @ApiPropertyOptional({
    example: 'Van Type A',
    description: 'Assigned vehicle type Name',
  })
  assignedVehicleName?: string;

  @ApiPropertyOptional({
    example: 'Ford Transit (ABC-123)',
    description: 'Assigned vehicle description',
  })
  assignedVehicleDescription?: string;

  // Pricing Information
  @ApiProperty({
    example: 45.99,
    description: 'Base price of the order',
  })
  basePrice: number;

  @ApiPropertyOptional({
    example: 15.0,
    description: 'Additional cost for options',
  })
  optionsPrice?: number;

  @ApiPropertyOptional({
    example: 5.0,
    description: 'Miscellaneous price adjustments',
  })
  miscAdjustment?: number;

  @ApiPropertyOptional({
    example: -10.0,
    description: 'Customer-specific price adjustments',
  })
  customerAdjustment?: number;

  @ApiProperty({
    example: 55.99,
    description: 'Total price of the order',
  })
  totalPrice: number;

  // Billing Information
  @ApiProperty({
    enum: BillingStatus,
    example: BillingStatus.NotBilled,
    description: 'Current billing status',
  })
  billingStatus: BillingStatus;

  @ApiProperty({
    enum: PaymentStatus,
    example: PaymentStatus.Pending,
    description: 'Current payment status',
  })
  paymentStatus: PaymentStatus;

  // Stats/Metrics
  @ApiPropertyOptional({
    example: 10.5,
    description: 'Distance in selected units',
  })
  distance?: number;

  @ApiProperty({
    enum: DistanceUnit,
    example: DistanceUnit.Kilometers,
    description: 'Unit of distance measurement',
  })
  distanceUnit: DistanceUnit;

  @ApiPropertyOptional({
    example: '01:30:00',
    description: 'Estimated duration of the delivery',
  })
  estimatedDuration?: string;

  @ApiPropertyOptional({
    example: 'Lumigo Express 2 hour',
    description: 'Price set name',
  })
  priceSet?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655499999',
    description: 'Price set ID',
  })
  priceSetId?: string;

  @ApiPropertyOptional({
    example: 'express_2_hour',
    description: 'Service level name',
  })
  serviceLevel?: string;

  @ApiPropertyOptional({
    example: 'Next gen',
    description: 'Collection company name',
  })
  collectionCompanyName?: string;

  @ApiPropertyOptional({
    example: 'FleetXpo',
    description: 'Delivery company name',
  })
  deliveryCompanyName?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655499999',
    description: 'Package templete ID',
  })
  packageTemplateId?: string;

  // Additional Fields
  @ApiPropertyOptional({
    example: 'Urgent delivery of medical supplies',
    description: 'Order description',
  })
  description?: string;

  @ApiPropertyOptional({
    example: 'Customer requested expedited shipping',
    description: 'Comments about the order',
  })
  comments?: string;

  @ApiPropertyOptional({
    example: 'VIP customer - handle with care',
    description: 'Internal notes (not visible to customer)',
  })
  internalNotes?: string;

  @ApiProperty({
    example: true,
    description: 'Is cash on delivery',
  })
  isCod: boolean;

  @ApiPropertyOptional({
    example: 10,
    description: 'Cash on delivery amount',
  })
  codAmount?: number;

  @ApiPropertyOptional({
    type: [OrderItemResponseDto],
    description: 'Items included in this order',
  })
  items?: OrderItemResponseDto[];

  @ApiPropertyOptional({
    example: 100,
    description: 'Custom total price for the order',
  })
  customTotalPrice?: number;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    description: 'Custom pricing summary for the order',
  })
  customPricingSummary?: Record<string, any>;

  @ApiPropertyOptional({
    type: 'object',
    additionalProperties: true,
    description: 'Pricing summary for the order',
  })
  pricingSummary?: Record<string, any>;

  @ApiProperty({
    example: '2025-04-08T12:34:56Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2025-04-08T14:22:33Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}

export interface OrderAttachments {
  pickupSignature: string | null;
  deliverySignature: string | null;
  images: string[];
}
