import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderStatus } from '../domain/order.types';
import { OrderResponseDto } from './order-response.dto';

// DTO for status history entries
export class OrderStatusHistoryDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique identifier for the status history entry',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655441111',
    description: 'Order ID',
  })
  orderId: string;

  @ApiPropertyOptional({
    enum: OrderStatus,
    example: OrderStatus.Draft,
    description: 'Previous status of the order',
  })
  previousStatus?: OrderStatus;

  @ApiProperty({
    enum: OrderStatus,
    example: OrderStatus.Assigned,
    description: 'New status of the order',
  })
  newStatus: OrderStatus;

  @ApiPropertyOptional({
    example: 'Customer requested status change',
    description: 'Reason for the status change',
  })
  reason?: string;

  @ApiPropertyOptional({
    example: 'Called customer to confirm details',
    description: 'Additional comments about the status change',
  })
  comments?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'User ID of who changed the status',
  })
  changedBy: string;

  @ApiPropertyOptional({
    example: 'John Smith',
    description: 'Name of the user who changed the status',
  })
  changedByName?: string;

  @ApiProperty({
    example: '2025-04-08T12:34:56Z',
    description: 'Timestamp when the status was changed',
  })
  changedAt: Date;
}

// DTO for assignment history entries
export class OrderAssignmentHistoryDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique identifier for the assignment history entry',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655441111',
    description: 'Order ID',
  })
  orderId: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'Previous driver ID',
  })
  previousDriverId?: string;

  @ApiPropertyOptional({
    example: 'Jane Doe',
    description: 'Previous driver name',
  })
  previousDriverName?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655443333',
    description: 'New driver ID',
  })
  newDriverId: string;

  @ApiPropertyOptional({
    example: 'John Smith',
    description: 'New driver name',
  })
  newDriverName?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655444444',
    description: 'Previous vehicle ID',
  })
  previousVehicleId?: string;

  @ApiPropertyOptional({
    example: 'Ford Transit (ABC-123)',
    description: 'Previous vehicle description',
  })
  previousVehicleDescription?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655455555',
    description: 'New vehicle ID',
  })
  newVehicleId?: string;

  @ApiPropertyOptional({
    example: 'Toyota HiAce (XYZ-789)',
    description: 'New vehicle description',
  })
  newVehicleDescription?: string;

  @ApiPropertyOptional({
    example: 'Driver shift change',
    description: 'Reason for the assignment change',
  })
  reason?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655466666',
    description: 'User ID of who made the assignment change',
  })
  assignedBy: string;

  @ApiPropertyOptional({
    example: 'Sarah Johnson',
    description: 'Name of the user who made the assignment change',
  })
  assignedByName?: string;

  @ApiProperty({
    example: '2025-04-08T14:22:33Z',
    description: 'Timestamp when the assignment was changed',
  })
  assignedAt: Date;
}

// DTO for stop history entries
export class OrderStopHistoryDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique identifier for the stop history entry',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655441111',
    description: 'Order ID',
  })
  orderId: string;

  @ApiProperty({
    example: 'Collection',
    description: 'Type of stop (Collection or Delivery)',
  })
  stopType: string;

  @ApiProperty({
    example: '2025-04-10T09:00:00Z',
    description: 'Scheduled time for the stop',
  })
  scheduledTime: Date;

  @ApiPropertyOptional({
    example: '2025-04-10T09:15:30Z',
    description: 'Actual time when the stop occurred',
  })
  actualTime?: Date;

  @ApiPropertyOptional({
    example: { latitude: 40.7128, longitude: -74.006 },
    description: 'Location data for the stop',
  })
  locationData?: Record<string, any>;

  @ApiPropertyOptional({
    example: 'Customer was not available, package left with receptionist',
    description: 'Notes about the stop',
  })
  notes?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'User ID of who created/updated the stop',
  })
  updatedBy: string;

  @ApiPropertyOptional({
    example: 'John Smith',
    description: 'Name of the user who created/updated the stop',
  })
  updatedByName?: string;

  @ApiProperty({
    example: '2025-04-08T12:34:56Z',
    description: 'Timestamp when the stop was created/updated',
  })
  updatedAt: Date;
}

// Main DTO for detailed order response
export class OrderDetailResponseDto extends OrderResponseDto {
  @ApiPropertyOptional({
    type: [OrderStatusHistoryDto],
    description: 'History of status changes for this order',
  })
  statusHistory?: OrderStatusHistoryDto[];

  @ApiPropertyOptional({
    type: [OrderAssignmentHistoryDto],
    description: 'History of driver/vehicle assignments for this order',
  })
  assignmentHistory?: OrderAssignmentHistoryDto[];

  @ApiPropertyOptional({
    type: [OrderStopHistoryDto],
    description: 'History of stops (collection/delivery) for this order',
  })
  stopHistory?: OrderStopHistoryDto[];
}
