import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderStatus } from '../domain/order.types';

export class OrderStatusHistoryResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique ID for this status history record',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655441111',
    description: 'Order ID',
  })
  orderId: string;

  @ApiPropertyOptional({
    enum: OrderStatus,
    example: OrderStatus.Pending,
    description: 'Previous status of the order (if any)',
  })
  previousStatus?: OrderStatus;

  @ApiProperty({
    enum: OrderStatus,
    example: OrderStatus.GoingForDelivery,
    description: 'New status of the order',
  })
  newStatus: OrderStatus;

  @ApiPropertyOptional({
    example: 'Driver arrived at collection point',
    description: 'Reason for the status change',
  })
  reason?: string;

  @ApiPropertyOptional({
    example: 'Customer was notified via SMS',
    description: 'Additional comments about the status change',
  })
  comments?: string;

  @ApiPropertyOptional({
    example: {
      latitude: 40.7128,
      longitude: -74.006,
      accuracy: 10,
      address: '123 Main St, New York, NY 10001',
    },
    description: 'Location data at the time of status change',
  })
  locationData?: Record<string, any>;

  @ApiPropertyOptional({
    example: {
      source: 'driver_app',
      connectionType: 'cellular',
      batteryLevel: 0.75,
    },
    description: 'Additional metadata about this status change',
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655442222',
    description: 'ID of the user who changed the status',
  })
  changedBy: string;

  @ApiPropertyOptional({
    example: 'John Driver',
    description: 'Name of the user who changed the status',
  })
  changedByName?: string;

  @ApiProperty({
    example: '2025-04-08T12:34:56Z',
    description: 'When this status change occurred',
  })
  changedAt: Date;
}
