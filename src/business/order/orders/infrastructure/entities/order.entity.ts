import {
  <PERSON>umn,
  CreateDate<PERSON>olumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { ContactEntity } from '@app/business/user/contacts/infrastructure/persistence/relational/entities/contact.entity';
import { AddressEntity } from '@app/business/address/addresses/infrastructure/entities/address.entity';
import { ZoneEntity } from '@app/business/zone/zones/infrastructure/entities/zone.entity';
import { VehicleTypeEntity } from '@app/business/vehicle/vehicle-types/infrastructure/entities/vehicle-type.entity';
import { VehicleEntity } from '@app/business/vehicle/vehicles/infrastructure/entities/vehicle.entity';
import { PriceSetEntity } from '@app/business/pricing/price-sets/infrastructure/entities/price-set.entity';
import { PackageTemplateEntity } from '@app/business/order/package-templates/infrastructure/entities/package-template.entity';
import { OrderItemEntity } from './order-item.entity';
import { OrderStatusHistoryEntity } from './order-status-history.entity';
import { OrderAssignmentHistoryEntity } from './order-assignment-history.entity';
import { OrderStopHistoryEntity } from './order-stop-history.entity';
import {
  OrderStatus,
  BillingStatus,
  PaymentStatus,
  DistanceUnit,
} from '../../domain/order.types';

@Entity('orders')
export class OrderEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid', { name: 'tenant_id' })
  @Index()
  tenantId: string;

  @AutoMap()
  @Column({ length: 100, unique: true, name: 'tracking_number' })
  trackingNumber: string;

  @AutoMap()
  @Column({ length: 100, nullable: true, name: 'reference_number' })
  referenceNumber: string;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.Draft,
    name: 'status',
  })
  status: OrderStatus;

  // Customer Information
  @AutoMap()
  @Column('uuid', { name: 'customer_id' })
  customerId: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'customer_id' })
  customer: UserEntity;

  @AutoMap()
  @Column('uuid', { name: 'requested_by_id' })
  requestedById: string;

  @ManyToOne(() => ContactEntity)
  @JoinColumn({ name: 'requested_by_id' })
  requestedBy: ContactEntity;

  @AutoMap()
  @Column('uuid', { name: 'submitted_by_id' })
  submittedById: string;

  @ManyToOne(() => ContactEntity)
  @JoinColumn({ name: 'submitted_by_id' })
  submittedBy: ContactEntity;

  // Package Template Info
  @AutoMap()
  @Column('uuid', { nullable: true, name: 'package_template_id' })
  packageTemplateId: string;

  @ManyToOne(() => PackageTemplateEntity)
  @JoinColumn({ name: 'package_template_id' })
  packageTemplate: PackageTemplateEntity;

  // Collection Information
  @AutoMap()
  @Column('uuid', { name: 'collection_address_id' })
  collectionAddressId: string;

  @ManyToOne(() => AddressEntity)
  @JoinColumn({ name: 'collection_address_id' })
  collectionAddress: AddressEntity;

  @AutoMap()
  @Column({ length: 255, nullable: true, name: 'collection_contact_name' })
  collectionContactName: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true, name: 'collection_instructions' })
  collectionInstructions: string;

  @AutoMap()
  @Column({ default: false, name: 'collection_signature_required' })
  collectionSignatureRequired: boolean;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'collection_signature_data' })
  collectionSignatureData: Record<string, any>;

  @AutoMap()
  @Column({
    type: 'timestamptz',
    nullable: true,
    name: 'scheduled_collection_time',
  })
  scheduledCollectionTime: Date;

  @AutoMap()
  @Column({
    type: 'timestamptz',
    nullable: true,
    name: 'actual_collection_time',
  })
  actualCollectionTime: Date;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'collection_zone_id' })
  collectionZoneId: string;

  @ManyToOne(() => ZoneEntity)
  @JoinColumn({ name: 'collection_zone_id' })
  collectionZone: ZoneEntity;

  // Delivery Information
  @AutoMap()
  @Column('uuid', { name: 'delivery_address_id' })
  deliveryAddressId: string;

  @ManyToOne(() => AddressEntity)
  @JoinColumn({ name: 'delivery_address_id' })
  deliveryAddress: AddressEntity;

  @AutoMap()
  @Column({ length: 255, nullable: true, name: 'delivery_contact_name' })
  deliveryContactName: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true, name: 'delivery_instructions' })
  deliveryInstructions: string;

  @AutoMap()
  @Column({ default: false, name: 'delivery_signature_required' })
  deliverySignatureRequired: boolean;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'delivery_signature_data' })
  deliverySignatureData: Record<string, any>;

  @AutoMap()
  @Column({
    type: 'timestamptz',
    nullable: true,
    name: 'scheduled_delivery_time',
  })
  scheduledDeliveryTime: Date;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true, name: 'actual_delivery_time' })
  actualDeliveryTime: Date;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'delivery_zone_id' })
  deliveryZoneId: string;

  @ManyToOne(() => ZoneEntity)
  @JoinColumn({ name: 'delivery_zone_id' })
  deliveryZone: ZoneEntity;

  // Package Information
  @AutoMap()
  @Column({ default: 1, name: 'total_items' })
  totalItems: number;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    name: 'total_weight',
  })
  totalWeight: number;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    name: 'total_volume',
  })
  totalVolume: number;

  //Declared value = totalDeclaredValue entered by user for individual packages and the flag will be isInsurance

  @AutoMap()
  @Column({ default: false, name: 'is_insurance' })
  isInsurance: boolean;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    name: 'declared_value',
  })
  declaredValue: number;

  // Vehicle and Assignment Information
  @AutoMap()
  @Column('uuid', { nullable: true, name: 'vehicle_type_id' })
  vehicleTypeId: string;

  @ManyToOne(() => VehicleTypeEntity)
  @JoinColumn({ name: 'vehicle_type_id' })
  vehicleType: VehicleTypeEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'assigned_driver_id' })
  assignedDriverId: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'assigned_driver_id' })
  assignedDriver: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'assigned_vehicle_id' })
  assignedVehicleId: string;

  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'assigned_vehicle_id' })
  assignedVehicle: VehicleEntity;

  @AutoMap()
  @Column({ default: false, name: 'is_cod' })
  isCod: boolean;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    name: 'cod_amount',
  })
  codAmount: number;

  @AutoMap()
  @Column({ default: false, name: 'cod_collected' })
  codCollected: boolean;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true, name: 'cod_collection_date' })
  codCollectionDate: Date;

  // Pricing Information
  @AutoMap()
  @Column('uuid', { nullable: true, name: 'price_set_id' })
  priceSetId: string;

  @ManyToOne(() => PriceSetEntity)
  @JoinColumn({ name: 'price_set_id' })
  priceSet: PriceSetEntity;

  @AutoMap()
  @Column({ length: 50, nullable: true, name: 'base_price_type' })
  basePriceType: string;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    name: 'base_price',
  })
  basePrice: number;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    name: 'options_price',
  })
  optionsPrice: number;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    name: 'misc_adjustment',
  })
  miscAdjustment: number;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    name: 'customer_adjustment',
  })
  customerAdjustment: number;

  @AutoMap()
  @Column({
    name: 'total_price',
    type: 'decimal',
    precision: 10,
    scale: 2,
    generatedType: 'STORED',
    asExpression: `COALESCE(base_price, 0) +
                   COALESCE(options_price, 0) +
                   COALESCE(misc_adjustment, 0) +

                   COALESCE(customer_adjustment, 0)`,
  })
  totalPrice: number;

  // Billing Information
  @AutoMap()
  @Column({
    type: 'enum',
    enum: BillingStatus,
    default: BillingStatus.NotBilled,
    name: 'billing_status',
  })
  billingStatus: BillingStatus;

  @AutoMap()
  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.Pending,
    name: 'payment_status',
  })
  paymentStatus: PaymentStatus;

  // Stats/Metrics
  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    name: 'distance',
  })
  distance: number;

  @AutoMap()
  @Column({
    length: 10,
    default: DistanceUnit.Kilometers,
    name: 'distance_unit',
  })
  distanceUnit: DistanceUnit;

  @AutoMap()
  @Column({ type: 'interval', nullable: true, name: 'estimated_duration' })
  estimatedDuration: string;

  @AutoMap()
  @Column({ type: 'interval', nullable: true, name: 'actual_duration' })
  actualDuration: string;

  // Additional Fields
  @AutoMap()
  @Column({ type: 'text', nullable: true, name: 'description' })
  description: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true, name: 'comments' })
  comments: string;

  //Special instructions for driver from customer
  @AutoMap()
  @Column({ type: 'text', nullable: true, name: 'internal_notes' })
  internalNotes: string;

  @AutoMap()
  @Column({ type: 'jsonb', default: '{}', name: 'custom_fields' })
  customFields: Record<string, any>;

  @AutoMap()
  @Column({ type: 'jsonb', default: '{}', name: 'metadata' })
  metadata: Record<string, any>;

  // System Fields
  @AutoMap()
  @Column({ default: false, name: 'is_locked' })
  isLocked: boolean;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'locked_by_id' })
  lockedBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'locked_by' })
  lockedByUser: UserEntity;

  @AutoMap()
  @Column({ type: 'text', nullable: true, name: 'lock_reason' })
  lockReason: string;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true, name: 'lock_timestamp' })
  lockTimestamp: Date;

  @AutoMap()
  @Column({ default: false, name: 'is_deleted' })
  isDeleted: boolean;

  @AutoMap()
  @DeleteDateColumn({ type: 'timestamptz', nullable: true, name: 'deleted_at' })
  deletedAt: Date;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  updatedAt: Date;

  @AutoMap()
  @Column('uuid', { name: 'created_by_id' })
  createdBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'created_by' })
  createdByUser: UserEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'updated_by_id' })
  updatedBy: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'updated_by' })
  updatedByUser: UserEntity;

  // Relationships with child entities
  @OneToMany(() => OrderItemEntity, (item) => item.order)
  items: OrderItemEntity[];

  @OneToMany(() => OrderStatusHistoryEntity, (status) => status.order)
  statusHistory: OrderStatusHistoryEntity[];

  @OneToMany(
    () => OrderAssignmentHistoryEntity,
    (assignment) => assignment.order,
  )
  assignmentHistory: OrderAssignmentHistoryEntity[];

  @OneToMany(() => OrderStopHistoryEntity, (stop) => stop.order)
  stopHistory: OrderStopHistoryEntity[];

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'pricing_summary' })
  pricingSummary: Record<string, any>;

  @AutoMap()
  @Column({ type: 'jsonb', nullable: true, name: 'custom_pricing_summary' })
  customPricingSummary: Record<string, any>;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    name: 'custom_total_price',
    nullable: true,
  })
  customTotalPrice: number;
}
