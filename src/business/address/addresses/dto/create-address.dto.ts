import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  IsBoolean,
  IsUUID,
  Matches,
  IsNumber,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { trimTransformer } from '@utils/transformers/lower-case.transformer';

export class UpdateAddressDto {
  @AutoMap()
  @ApiProperty({ example: 'Insight Technologies' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  name: string;

  @AutoMap()
  @ApiProperty({ example: 'Swift Logistics' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  companyName: string;

  @AutoMap()
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  @Transform(trimTransformer)
  email: string;

  @AutoMap()
  @ApiProperty({ example: 'US' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  countryCode: string;

  @AutoMap()
  @ApiProperty({ example: '1234567890' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+$/, { message: 'Phone number must contain only digits' })
  phoneNumber: string;

  @AutoMap()
  @ApiProperty({ example: 101 })
  @IsString()
  @IsOptional()
  @Matches(/^\d+$/, { message: 'Phone extension must contain only digits' })
  phoneExtension: string;

  @AutoMap()
  @ApiProperty({ example: '123 Main St' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  addressLine1: string;

  @AutoMap()
  @ApiProperty({ example: 'Apt 2' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  addressLine2: string;

  @AutoMap()
  @ApiProperty({ example: 'Montreal' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  city: string;

  @AutoMap()
  @ApiProperty({ example: 'Quebec' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  province: string;

  @AutoMap()
  @ApiProperty({ default: 'H45 2Y7' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  postalCode: string;

  @AutoMap()
  @ApiProperty({ example: 'Canada' })
  @IsString()
  @IsNotEmpty()
  @Transform(trimTransformer)
  country: string;

  @AutoMap()
  @ApiProperty({ example: 'Address notes' })
  @IsString()
  @IsOptional()
  @Transform(trimTransformer)
  notes: string;

  @AutoMap()
  @ApiProperty({
    example: 45.508888,
    description: 'Latitude coordinate of the address location',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Min(-90)
  @Max(90)
  latitude: number;

  @AutoMap()
  @ApiProperty({
    example: -73.561668,
    description: 'Longitude coordinate of the address location',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Min(-180)
  @Max(180)
  longitude: number;

  @AutoMap()
  @ApiProperty({ example: true })
  @IsBoolean()
  @IsOptional()
  @Transform(trimTransformer)
  isFavoriteForPickup: string;

  @AutoMap()
  @ApiProperty({ example: true })
  @IsBoolean()
  @IsOptional()
  @Transform(trimTransformer)
  isFavoriteForDelivery: string;

  @AutoMap()
  @ApiProperty({ example: true })
  @IsBoolean()
  @IsOptional()
  @Transform(trimTransformer)
  isDefaultForPickup: string;

  @AutoMap()
  @ApiProperty({ example: true })
  @IsBoolean()
  @IsOptional()
  @Transform(trimTransformer)
  isDefaultForDelivery: string;
}

export class CreateAddressDto extends UpdateAddressDto {
  @AutoMap()
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  @IsNotEmpty()
  customerId: string;
}
